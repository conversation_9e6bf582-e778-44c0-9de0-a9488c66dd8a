#include<iostream>
using namespace std;

int main() {
    int a = 1;
    int b = 6;

    cout << "a & b: " << (a & b) << endl;  // bitwise AND
    cout << "a | b: " << (a | b) << endl;  // bitwise OR
    cout << "~a: " << (~a) << endl;        // bitwise NOT
    cout << "a ^ b: " << (a ^ b) << endl;  // bitwise XOR

    // cout<<(17>>1)<<endl;
    // cout<<(17>>2)<<endl;
    // cout<<(19<<1)<<endl;
    // cout<<(21<<2)<<endl;

    int i=1;
    cout<<(i++)<<endl;
    cout<<(++i)<<endl;
    cout<<(i--)<<endl;
    cout<<(--i)<<endl;

    return 0;
}
