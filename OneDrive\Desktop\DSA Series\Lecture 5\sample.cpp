#include<iostream>
using namespace std;
class Solution {
public:
    int subtractProductAndSum(int n) {
        int sum=0;
        int mul=1;
        for(int i=0;i<n;i++)
        {
            int rem=n%10;
            sum=sum+rem;
            mul=mul+rem;
            n=n/10;
        }
        int result=mul-sum;
       return result;
    
    }
    
};
int main()
{
    int n;
    cin>>n;
    Solution obj;
    int result=obj.subtractProductAndSum(n);
    cout<<result;
}