#include <iostream>
using namespace std;
int main()
{
    int n;
    cout<<"Enetr Value of n:";
    cin>>n;

    // cout<<"Printing 1 to n:"<<endl;
    // for(int i=1;i<=n;i++)
    // {
    //     cout<<i<<endl;
    // }

    // int sum=0;
    // for(int i=1;i<=n;i++)
    // {
    //     sum=sum+i;
    // }
    // cout<<"Sum of 1 to n is:"<<sum<<endl;

    // int fno=0,sno=1;
    // int tno;

    // cout<<fno<<" "<<sno<<" ";
    // for(int i=2;i<=n;i++)
    // {
    //     tno=fno+sno;
    //     cout<<tno<<" ";
    //     fno=sno;
    //     sno=tno;
    // }
    // cout<<endl;

    int flag=0;
    for(int i=2;i<n;i++)
    {
        if(n%i==0)
        {
            flag=1;
            break;
        }
    }
    if(flag==1)
    {
        cout<<"Not Prime"<<endl;
    }
    else
    {
        cout<<"Prime"<<endl;
    }
   
    return 0;
}