#include<iostream>
using namespace std;

int main()
{
    // int n;
    // cout<<"Enter a limit:";
    // cin>>n;
    // for(int i=1;i<=n;i++)
    // {
    //     for(int j=1;j<=n;j++)
    //     {
    //         cout<<"*";
    //     }
    //     cout<<endl;
    // }
    int n;
    
    // cout<<"Enter a limit:";
    // cin>>n;
    // for(int i=1;i<=n;i++)
    // {
    //     char ch='A';
    //     for(int j=1;j<=n;j++)
    //     {
    //         cout<<ch;
    //         ch=ch+1;
    //     }
    //     cout<<endl;
    // }

    cout<<"Enter a limit:";
    cin>>n;
    int num=1;
    for(int i=1;i<=n;i++)
    {
        for(int j=1;j<=n;j++)
        {
            cout<<num;
            num=num+1;
        }
        cout<<endl;
    }
    // 12345
    // 678910
    // 1112131415
    // 1617181920
    // 2122232425

    cout<<"Enter a limit:";
    cin>>n;
    char ch='A';
    for(int i=1;i<=n;i++)
    {
        for(int j=1;j<=n;j++)
        {
            cout<<ch;
            ch=ch+1;
        }
        cout<<endl;
    }
    // ABCDE
    // FGHIJ
    // KLMNO
    // PQRST
    // UVWXY

    
    return 0;
}