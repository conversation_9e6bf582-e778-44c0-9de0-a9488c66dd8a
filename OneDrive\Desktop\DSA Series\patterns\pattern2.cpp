#include <iostream>
using namespace std;

int main()
{
    // int n;
    // cout << "Enter a limit:";
    // cin >> n;
    
    // for (int i = 0; i <= n; i++)
    // {
    //     for (int j = 0; j <i+1; j++)
    //     {
    //         cout <<"* ";
    //     }
    //     cout << endl;
    // }
    // o/p:* 
    //     * *
    //     * * *
    //     * * * *
    //     * * * * *
    //     * * * * * *

    int n;
    cout << "Enter a limit:";
    cin >> n;
    
    // for (int i = 0; i <= n; i++)
    // {
    //     for (int j = 0; j <i+1; j++)
    //     {
    //         cout <<" "<<(i+1);
    //     }
    //     cout << endl;
    // }

    // // O/P:
    // // 1
    // // 2 2
    // // 3 3 3
    // // 4 4 4 4
    // // 5 5 5 5 5
    // // 6 6 6 6 6 6

    // int n;
    // char ch='A';

    // cout << "Enter a limit:";
    // cin >> n;
    
    // for (int i = 0; i <= n; i++)
    // {
    //     for (int j = 0; j <i+1; j++)
    //     {
    //         cout <<ch;
            
    //     }
    //     ch++;
    //     cout << endl;
    // }
    
    // O/P:
    // A
    // BB
    // CCC
    // DDDD
    // EEEEE
    // FFFFFF


    for(int i=0;i<n;i++)
    {
        for(int j=0;j<i;j++)
        {
            cout<<" ";  
        }

        for(int k=0;k<n-i;k++)
        {
            cout<<(i+1);
        }
        cout<<endl;
    }

    return 0;
}