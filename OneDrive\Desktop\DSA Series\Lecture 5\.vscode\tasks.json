{"tasks": [{"type": "cppbuild", "label": "C/C++: gcc.exe build active file", "command": "C:/MinGW/bin/gcc.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe", ""], "options": {"cwd": "C:/MinGW/bin"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "Task generated by <PERSON>bugger."}], "version": "2.0.0"}